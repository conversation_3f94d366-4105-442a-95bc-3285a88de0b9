<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\Epp\Services\EppDomainService;
use App\Modules\Notification\Services\NotificationService;
use App\Util\Constant\UserDomainStatus;
use App\Util\Constant\DomainStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use App\Modules\CustomLogger\Services\AuthLogger;

class DomainEppCancellationJobService
{
    public static function instance()
    {
        return new self;
    }

    public function eppDelete(array $domain): void
    {
        $eppInfo = EppDomainService::instance()->callEppDomainInfo($domain['domainName'])['data'];
        $datastoreInfo = EppDomainService::instance()->callDatastoreDomainInfo($domain['domainName'])['data'];
        
        if (in_array('clientDeleteProhibited', $eppInfo['status'])) {
            app(Authlogger::class)->info("Domain {$domain['domainName']} has clientDeleteProhibited status, cannot delete");
            return;
        }

        EppDomainService::instance()->callEppDomainDelete($domain['domainName']);
        EppDomainService::instance()->callDatastoreDomainDelete($domain['domainName']);

        $this->updateLocalDatabase($domain);
    }

    private function updateLocalDatabase(array $domain): void
    {
        $timestamp = now();

        $updates = [
            'status'     => UserDomainStatus::DELETED,
            'deleted_at' => $timestamp,
            'updated_at' => $timestamp,
        ];

        DB::client()->table('registered_domains')
            ->where('domain_id', $domain['domainId'])
            ->update($updates);

        DB::client()->table('domains')
            ->where('id', $domain['domainId'])
            ->update($updates);

        $this->updateDomainCancellationRequest($domain);

        DB::client()->table('pending_domain_deletions')->insert([
            'registered_domain_id' => $domain['domainId'],
            'deleted_by' => $domain['userEmail'],
            'deleted_at' => $timestamp,
            'created_at' => $timestamp,
            'updated_at' => $timestamp,
        ]);
    }

    private function updateDomainCancellationRequest(array $domain): void
    {
        $exists = DB::client()
            ->table('domain_cancellation_requests')
            ->where('domain_id', $domain['domainId'])
            ->exists();

        if (!$exists) {
            $this->createDomainCancellationRequest($domain);
            return;
        }

        $date = Carbon::parse($domain['createdDate']);
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $domain['domainId'])
            ->update([
                'support_agent_id'   => Auth::id(),
                'support_agent_name' => Auth::check() ? (Auth::user()->name . ' (' . Auth::user()->email . ')') : 'System Job',
                'deleted_at'         => now(),
                'feedback_date'      => now(),
                'support_note'       => $domain['supportNote'] ?? 'Domain deleted via job processing',
                'is_refunded'        => $is_refunded,
            ]);
    }

    private function createDomainCancellationRequest(array $domain): void
    {
        $date = Carbon::parse($domain['createdDate']);
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        DB::client()->table('domain_cancellation_requests')->insert([
            'user_id'             => $domain['userId'],
            'domain_id'           => $domain['domainId'],
            'reason'              => $domain['reason'],
            'support_agent_id'    => Auth::id(),
            'support_agent_name'  => Auth::check() ? (Auth::user()->name . ' (' . Auth::user()->email . ')') : 'System Job',
            'deleted_at'          => now(),
            'feedback_date'       => now(),
            'support_note'        => $domain['supportNote'] ?? 'Domain deleted via job processing',
            'is_refunded'         => $is_refunded,
            'created_at'          => now(),
            'updated_at'          => now(),
        ]);
    }
}
