[2025-08-19 02:09:40] local.ERROR: SQLSTATE[42703]: Undefined column: 7 ERROR:  column "created_at" of relation "domain_cancellation_requests" does not exist
LINE 1: ..., "feedback_date", "support_note", "is_refunded", "created_a...
                                                             ^ (Connection: client, SQL: insert into "domain_cancellation_requests" ("user_id", "domain_id", "reason", "support_agent_id", "support_agent_name", "deleted_at", "feedback_date", "support_note", "is_refunded", "created_at", "updated_at") values (7, 7, test test test tes tsetse, ?, System Job, 2025-08-19 02:09:40, 2025-08-19 02:09:40, Domain deleted via job processing, 0, 2025-08-19 02:09:40, 2025-08-19 02:09:40))  
[2025-08-19 02:09:40] local.INFO: number of attempts: 1  
[2025-08-19 02:09:40] local.ERROR: SQLSTATE[42703]: Undefined column: 7 ERROR:  column "created_at" of relation "domain_cancellation_requests" does not exist
LINE 1: ..., "feedback_date", "support_note", "is_refunded", "created_a...
                                                             ^ (Connection: client, SQL: insert into "domain_cancellation_requests" ("user_id", "domain_id", "reason", "support_agent_id", "support_agent_name", "deleted_at", "feedback_date", "support_note", "is_refunded", "created_at", "updated_at") values (7, 7, test test test tes tsetse, ?, System Job, 2025-08-19 02:09:40, 2025-08-19 02:09:40, Domain deleted via job processing, 0, 2025-08-19 02:09:40, 2025-08-19 02:09:40))  
[2025-08-19 02:09:40] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"created_at\" of relation \"domain_cancellation_requests\" does not exist
LINE 1: ..., \"feedback_date\", \"support_note\", \"is_refunded\", \"created_a...
                                                             ^ (Connection: client, SQL: insert into \"domain_cancellation_requests\" (\"user_id\", \"domain_id\", \"reason\", \"support_agent_id\", \"support_agent_name\", \"deleted_at\", \"feedback_date\", \"support_note\", \"is_refunded\", \"created_at\", \"updated_at\") values (7, 7, test test test tes tsetse, ?, System Job, 2025-08-19 02:09:40, 2025-08-19 02:09:40, Domain deleted via job processing, 0, 2025-08-19 02:09:40, 2025-08-19 02:09:40))","code":"42703"}  
[2025-08-19 02:09:43] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
